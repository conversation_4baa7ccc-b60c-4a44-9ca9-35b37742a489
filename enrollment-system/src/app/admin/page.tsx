'use client';

import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { DashboardLayout, PageContainer, StatsCard, Card } from '@/components/layout/DashboardLayout';
import { 
  Users, 
  BookOpen, 
  GraduationCap, 
  DollarSign,
  TrendingUp,
  AlertTriangle,
  Plus,
  Settings,
  BarChart3,
  FileText
} from 'lucide-react';

export default function AdminDashboard() {
  const { user } = useAuth();

  return (
    <DashboardLayout
      title="Admin Dashboard"
      description="Manage your institution's enrollment system"
      actions={
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <BarChart3 className="h-4 w-4 mr-2" />
            Reports
          </Button>
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Add User
          </Button>
        </div>
      }
    >
      <PageContainer>
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatsCard
            title="Total Students"
            value="1,247"
            change={{ value: "12%", type: "increase" }}
            icon={<GraduationCap className="h-6 w-6 text-blue-600" />}
          />
          <StatsCard
            title="Active Teachers"
            value="89"
            change={{ value: "3", type: "increase" }}
            icon={<Users className="h-6 w-6 text-green-600" />}
          />
          <StatsCard
            title="Total Classes"
            value="156"
            change={{ value: "8", type: "increase" }}
            icon={<BookOpen className="h-6 w-6 text-purple-600" />}
          />
          <StatsCard
            title="Revenue (This Month)"
            value="₱2,450,000"
            change={{ value: "15%", type: "increase" }}
            icon={<DollarSign className="h-6 w-6 text-yellow-600" />}
          />
        </div>

        {/* Quick Actions and Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <Card title="Quick Actions">
            <div className="space-y-3">
              <Button className="w-full justify-start" variant="outline">
                <Users className="h-4 w-4 mr-2" />
                Manage Users
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <BookOpen className="h-4 w-4 mr-2" />
                Manage Courses
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <GraduationCap className="h-4 w-4 mr-2" />
                View Enrollments
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <DollarSign className="h-4 w-4 mr-2" />
                Financial Reports
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Settings className="h-4 w-4 mr-2" />
                System Settings
              </Button>
            </div>
          </Card>

          <Card title="System Alerts">
            <div className="space-y-3">
              <div className="flex items-start space-x-3 p-3 bg-red-50 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-red-800">Payment Overdue</p>
                  <p className="text-xs text-red-600">23 students have overdue payments</p>
                </div>
              </div>
              <div className="flex items-start space-x-3 p-3 bg-yellow-50 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-yellow-800">Enrollment Deadline</p>
                  <p className="text-xs text-yellow-600">Deadline approaching in 3 days</p>
                </div>
              </div>
              <div className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
                <FileText className="h-5 w-5 text-blue-500 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-blue-800">Pending Documents</p>
                  <p className="text-xs text-blue-600">45 documents awaiting verification</p>
                </div>
              </div>
            </div>
          </Card>

          <Card title="Recent Activity">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-900">New student registered</p>
                  <p className="text-xs text-gray-500">Alice Johnson - 5 min ago</p>
                </div>
                <Badge variant="success">New</Badge>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-900">Payment received</p>
                  <p className="text-xs text-gray-500">Bob Wilson - ₱25,000 - 15 min ago</p>
                </div>
                <Badge variant="default">Payment</Badge>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-900">Class schedule updated</p>
                  <p className="text-xs text-gray-500">CS101 Section A - 1 hour ago</p>
                </div>
                <Badge variant="secondary">Update</Badge>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-900">New teacher added</p>
                  <p className="text-xs text-gray-500">Dr. Sarah Brown - 2 hours ago</p>
                </div>
                <Badge variant="success">New</Badge>
              </div>
            </div>
          </Card>
        </div>

        {/* Enrollment Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card title="Enrollment Statistics" description="Current semester enrollment data">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-700">Computer Science</span>
                <div className="flex items-center space-x-2">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full" style={{ width: '85%' }}></div>
                  </div>
                  <span className="text-sm text-gray-600">425/500</span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-700">Information Technology</span>
                <div className="flex items-center space-x-2">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div className="bg-green-600 h-2 rounded-full" style={{ width: '72%' }}></div>
                  </div>
                  <span className="text-sm text-gray-600">360/500</span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-700">Mathematics</span>
                <div className="flex items-center space-x-2">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div className="bg-purple-600 h-2 rounded-full" style={{ width: '60%' }}></div>
                  </div>
                  <span className="text-sm text-gray-600">180/300</span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-700">Business Administration</span>
                <div className="flex items-center space-x-2">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div className="bg-yellow-600 h-2 rounded-full" style={{ width: '90%' }}></div>
                  </div>
                  <span className="text-sm text-gray-600">270/300</span>
                </div>
              </div>
            </div>
          </Card>

          <Card title="Recent Enrollments" description="Latest student enrollments">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                      Student
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                      Course
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                      Date
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  <tr>
                    <td className="px-4 py-2 text-sm font-medium text-gray-900">
                      Alice Johnson
                    </td>
                    <td className="px-4 py-2 text-sm text-gray-500">Computer Science</td>
                    <td className="px-4 py-2 text-sm text-gray-500">Today</td>
                    <td className="px-4 py-2">
                      <Badge variant="success">Enrolled</Badge>
                    </td>
                  </tr>
                  <tr>
                    <td className="px-4 py-2 text-sm font-medium text-gray-900">
                      Bob Wilson
                    </td>
                    <td className="px-4 py-2 text-sm text-gray-500">Information Technology</td>
                    <td className="px-4 py-2 text-sm text-gray-500">Yesterday</td>
                    <td className="px-4 py-2">
                      <Badge variant="warning">Pending</Badge>
                    </td>
                  </tr>
                  <tr>
                    <td className="px-4 py-2 text-sm font-medium text-gray-900">
                      Carol Davis
                    </td>
                    <td className="px-4 py-2 text-sm text-gray-500">Mathematics</td>
                    <td className="px-4 py-2 text-sm text-gray-500">2 days ago</td>
                    <td className="px-4 py-2">
                      <Badge variant="success">Enrolled</Badge>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </Card>
        </div>
      </PageContainer>
    </DashboardLayout>
  );
}
