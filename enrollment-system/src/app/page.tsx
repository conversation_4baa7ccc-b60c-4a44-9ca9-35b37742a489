'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { GraduationCap, Users, BookOpen, CreditCard } from 'lucide-react';

export default function Home() {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading) {
      if (user) {
        router.push('/dashboard');
      } else {
        router.push('/auth/login');
      }
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-700">Loading Enrollment System...</h2>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center">
          <div className="flex justify-center mb-8">
            <div className="bg-blue-600 p-4 rounded-full">
              <GraduationCap className="h-12 w-12 text-white" />
            </div>
          </div>

          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Enrollment System
          </h1>

          <p className="text-xl text-gray-600 mb-12 max-w-3xl mx-auto">
            A comprehensive enrollment management system for educational institutions.
            Manage students, teachers, courses, and payments all in one place.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <Users className="h-8 w-8 text-blue-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">User Management</h3>
              <p className="text-gray-600">Manage students, teachers, administrators, and cashiers with role-based access control.</p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md">
              <BookOpen className="h-8 w-8 text-green-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Course Management</h3>
              <p className="text-gray-600">Create and manage courses, subjects, schedules, and class assignments.</p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md">
              <GraduationCap className="h-8 w-8 text-purple-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Enrollment</h3>
              <p className="text-gray-600">Streamlined enrollment process with real-time availability and grade tracking.</p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md">
              <CreditCard className="h-8 w-8 text-red-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Payment Processing</h3>
              <p className="text-gray-600">Handle tuition payments, generate receipts, and track financial records.</p>
            </div>
          </div>

          <div className="text-center">
            <p className="text-gray-600 mb-4">Redirecting to login page...</p>
            <div className="animate-pulse text-blue-600">Please wait...</div>
          </div>
        </div>
      </div>
    </div>
  );
}
