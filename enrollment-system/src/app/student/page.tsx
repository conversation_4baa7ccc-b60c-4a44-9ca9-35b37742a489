'use client';

import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { DashboardLayout, PageContainer, StatsCard, Card } from '@/components/layout/DashboardLayout';
import {
  BookOpen,
  Calendar,
  FileText,
  CreditCard,
  Bell,
  User,
  LogOut,
  TrendingUp,
  Clock,
  AlertCircle
} from 'lucide-react';

export default function StudentDashboard() {
  const { user } = useAuth();

  return (
    <DashboardLayout
      title="Student Dashboard"
      description={`Welcome back, ${user?.firstName} ${user?.lastName}`}
      actions={
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Bell className="h-4 w-4 mr-2" />
            Notifications
          </Button>
        </div>
      }
    >
      <PageContainer>
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <StatsCard
            title="Enrolled Subjects"
            value="6"
            change={{ value: "2", type: "increase" }}
            icon={<BookOpen className="h-6 w-6 text-blue-600" />}
          />
          <StatsCard
            title="Current Semester"
            value="1st Sem 2024-25"
            icon={<Calendar className="h-6 w-6 text-green-600" />}
          />
          <StatsCard
            title="Current GPA"
            value="3.75"
            change={{ value: "0.15", type: "increase" }}
            icon={<TrendingUp className="h-6 w-6 text-yellow-600" />}
          />
          <StatsCard
            title="Outstanding Balance"
            value="₱15,000"
            icon={<CreditCard className="h-6 w-6 text-red-600" />}
          />
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <Card title="Quick Actions">
            <div className="space-y-3">
              <Button className="w-full justify-start" variant="outline">
                <BookOpen className="h-4 w-4 mr-2" />
                Enroll in Subjects
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Calendar className="h-4 w-4 mr-2" />
                View Schedule
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <FileText className="h-4 w-4 mr-2" />
                View Grades
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <CreditCard className="h-4 w-4 mr-2" />
                Make Payment
              </Button>
            </div>
          </Card>

          <Card title="Recent Announcements">
            <div className="space-y-3">
              <div className="border-l-4 border-blue-400 pl-4">
                <p className="text-sm font-medium text-gray-900">Welcome to AY 2024-2025</p>
                <p className="text-xs text-gray-500 flex items-center">
                  <Clock className="h-3 w-3 mr-1" />
                  2 days ago
                </p>
              </div>
              <div className="border-l-4 border-yellow-400 pl-4">
                <p className="text-sm font-medium text-gray-900">Payment Deadline Reminder</p>
                <p className="text-xs text-gray-500 flex items-center">
                  <Clock className="h-3 w-3 mr-1" />
                  1 week ago
                </p>
              </div>
              <div className="border-l-4 border-green-400 pl-4">
                <p className="text-sm font-medium text-gray-900">New Library Hours</p>
                <p className="text-xs text-gray-500 flex items-center">
                  <Clock className="h-3 w-3 mr-1" />
                  2 weeks ago
                </p>
              </div>
            </div>
            <Button variant="link" className="mt-4 p-0">
              View all announcements
            </Button>
          </Card>

          <Card title="Upcoming Deadlines">
            <div className="space-y-3">
              <div className="flex justify-between items-center p-2 bg-red-50 rounded">
                <span className="text-sm text-gray-900">Tuition Payment</span>
                <Badge variant="destructive">Aug 15</Badge>
              </div>
              <div className="flex justify-between items-center p-2 bg-yellow-50 rounded">
                <span className="text-sm text-gray-900">CS101 Assignment</span>
                <Badge variant="warning">Aug 20</Badge>
              </div>
              <div className="flex justify-between items-center p-2 bg-blue-50 rounded">
                <span className="text-sm text-gray-900">Math Midterm Exam</span>
                <Badge variant="default">Aug 25</Badge>
              </div>
            </div>
          </Card>
        </div>

        {/* Current Enrollment */}
        <Card title="Current Enrollment" description="Your enrolled subjects for this semester">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Subject
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Section
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Schedule
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Instructor
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Units
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    CS101 - Introduction to Programming
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">A</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    MWF 8:00-9:30 AM
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    Prof. John Doe
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Badge variant="success">Enrolled</Badge>
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    MATH101 - College Algebra
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">A</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    TTh 2:00-3:30 PM
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    Prof. Jane Smith
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Badge variant="success">Enrolled</Badge>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </Card>
      </PageContainer>
    </DashboardLayout>
  );
}
