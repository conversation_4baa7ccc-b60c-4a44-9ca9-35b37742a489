import { PrismaClient } from '@prisma/client';

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma = globalForPrisma.prisma ?? new PrismaClient();

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;

// Database helper functions
export class DatabaseService {
  private client = prisma;

  // Generic CRUD operations
  async create<T>(model: string, data: any): Promise<T> {
    try {
      const result = await (this.client as any)[model].create({
        data,
      });
      return result;
    } catch (error) {
      console.error(`Error creating record in ${model}:`, error);
      throw error;
    }
  }

  async findById<T>(model: string, id: string, include?: any): Promise<T | null> {
    try {
      const result = await (this.client as any)[model].findUnique({
        where: { id },
        include,
      });
      return result;
    } catch (error) {
      console.error(`Error finding record in ${model}:`, error);
      throw error;
    }
  }

  async findMany<T>(
    model: string,
    options: {
      where?: any;
      include?: any;
      orderBy?: any;
      take?: number;
      skip?: number;
    } = {}
  ): Promise<T[]> {
    try {
      const result = await (this.client as any)[model].findMany(options);
      return result;
    } catch (error) {
      console.error(`Error finding records in ${model}:`, error);
      throw error;
    }
  }

  async update<T>(model: string, id: string, data: any, include?: any): Promise<T> {
    try {
      const result = await (this.client as any)[model].update({
        where: { id },
        data,
        include,
      });
      return result;
    } catch (error) {
      console.error(`Error updating record in ${model}:`, error);
      throw error;
    }
  }

  async delete(model: string, id: string): Promise<boolean> {
    try {
      await (this.client as any)[model].delete({
        where: { id },
      });
      return true;
    } catch (error) {
      console.error(`Error deleting record in ${model}:`, error);
      throw error;
    }
  }

  // Search functionality
  async search<T>(
    model: string,
    searchTerm: string,
    searchFields: string[],
    options: {
      where?: any;
      include?: any;
      take?: number;
    } = {}
  ): Promise<T[]> {
    try {
      const searchConditions = searchFields.map(field => ({
        [field]: {
          contains: searchTerm,
          mode: 'insensitive' as const,
        },
      }));

      const result = await (this.client as any)[model].findMany({
        where: {
          OR: searchConditions,
          ...options.where,
        },
        include: options.include,
        take: options.take,
      });

      return result;
    } catch (error) {
      console.error(`Error searching in ${model}:`, error);
      throw error;
    }
  }

  // Count records
  async count(model: string, where?: any): Promise<number> {
    try {
      const result = await (this.client as any)[model].count({
        where,
      });
      return result;
    } catch (error) {
      console.error(`Error counting records in ${model}:`, error);
      throw error;
    }
  }

  // Transaction support
  async transaction<T>(operations: ((tx: PrismaClient) => Promise<T>)[]): Promise<T[]> {
    try {
      const results = await this.client.$transaction(operations);
      return results;
    } catch (error) {
      console.error('Error in transaction:', error);
      throw error;
    }
  }

  // Disconnect
  async disconnect(): Promise<void> {
    await this.client.$disconnect();
  }
}

// Create a singleton instance
export const db = new DatabaseService();

// User-specific helper functions
export const userHelpers = {
  async findByEmail(email: string) {
    return await prisma.user.findUnique({
      where: { email },
      include: { profile: true },
    });
  },

  async createUser(data: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    role: 'ADMIN' | 'TEACHER' | 'STUDENT' | 'CASHIER';
  }) {
    return await prisma.user.create({
      data,
      include: { profile: true },
    });
  },

  async updateUser(id: string, data: any) {
    return await prisma.user.update({
      where: { id },
      data,
      include: { profile: true },
    });
  },

  async getUsersByRole(role: 'ADMIN' | 'TEACHER' | 'STUDENT' | 'CASHIER') {
    return await prisma.user.findMany({
      where: { role },
      include: { profile: true },
      orderBy: { createdAt: 'desc' },
    });
  },
};

// Class-specific helper functions
export const classHelpers = {
  async getClassesForTeacher(teacherId: string) {
    return await prisma.class.findMany({
      where: { teacherId },
      include: {
        subject: true,
        course: true,
        academicPeriod: true,
        schedules: true,
        enrollments: {
          include: {
            student: {
              include: { profile: true },
            },
          },
        },
      },
    });
  },

  async getAvailableClasses(academicPeriodId?: string) {
    return await prisma.class.findMany({
      where: {
        isActive: true,
        ...(academicPeriodId && { academicPeriodId }),
      },
      include: {
        subject: true,
        teacher: true,
        course: true,
        academicPeriod: true,
        schedules: true,
      },
    });
  },
};

// Enrollment-specific helper functions
export const enrollmentHelpers = {
  async getStudentEnrollments(studentId: string) {
    return await prisma.enrollment.findMany({
      where: { studentId },
      include: {
        class: {
          include: {
            subject: true,
            teacher: true,
            schedules: true,
            academicPeriod: true,
          },
        },
        grades: true,
      },
    });
  },

  async enrollStudent(studentId: string, classId: string) {
    return await prisma.$transaction(async (tx) => {
      // Create enrollment
      const enrollment = await tx.enrollment.create({
        data: {
          studentId,
          classId,
          status: 'ENROLLED',
        },
      });

      // Update enrolled count
      await tx.class.update({
        where: { id: classId },
        data: {
          enrolledCount: {
            increment: 1,
          },
        },
      });

      return enrollment;
    });
  },
};

// Payment-specific helper functions
export const paymentHelpers = {
  async getStudentPayments(studentId: string) {
    return await prisma.payment.findMany({
      where: { studentId },
      include: {
        processedByUser: true,
      },
      orderBy: { createdAt: 'desc' },
    });
  },

  async processPayment(paymentId: string, processedBy: string) {
    return await prisma.payment.update({
      where: { id: paymentId },
      data: {
        status: 'PAID',
        paidAt: new Date(),
        processedBy,
      },
    });
  },
};

export default prisma;
