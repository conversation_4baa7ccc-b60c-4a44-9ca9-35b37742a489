import { NextResponse, type NextRequest } from 'next/server';
import { verifyToken } from '@/lib/auth';

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Public routes that don't require authentication
  const publicRoutes = ['/auth/login', '/auth/register', '/auth/forgot-password'];
  const isPublicRoute = publicRoutes.includes(pathname);

  // Auth routes that should redirect if user is already logged in
  const authRoutes = ['/auth/login', '/auth/register'];
  const isAuthRoute = authRoutes.includes(pathname);

  // API routes that don't need middleware protection
  if (pathname.startsWith('/api/auth')) {
    return NextResponse.next();
  }

  // Get token from cookie or header
  const token = request.cookies.get('auth-token')?.value ||
                request.headers.get('authorization')?.replace('Bearer ', '');

  let user = null;
  if (token) {
    user = verifyToken(token);
  }

  // If user is not authenticated and trying to access protected route
  if (!user && !isPublicRoute) {
    const redirectUrl = new URL('/auth/login', request.url);
    redirectUrl.searchParams.set('redirectTo', pathname);
    return NextResponse.redirect(redirectUrl);
  }

  // If user is authenticated and trying to access auth routes, redirect to dashboard
  if (user && isAuthRoute) {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // Role-based route protection
  if (user && !isPublicRoute) {
    const userRole = user.role;

    // Define role-based route access
    const roleRoutes = {
      ADMIN: ['/admin', '/dashboard'],
      TEACHER: ['/teacher', '/dashboard'],
      STUDENT: ['/student', '/dashboard'],
      CASHIER: ['/cashier', '/dashboard'],
    };

    // Check if user has access to the requested route
    const hasAccess = Object.entries(roleRoutes).some(([role, routes]) => {
      if (userRole === role) {
        return routes.some(route => pathname.startsWith(route));
      }
      return false;
    });

    // Allow access to general dashboard and profile routes
    const generalRoutes = ['/dashboard', '/profile', '/api'];
    const isGeneralRoute = generalRoutes.some(route => pathname.startsWith(route));

    if (!hasAccess && !isGeneralRoute) {
      // Redirect to appropriate dashboard based on role
      const dashboardRoutes = {
        ADMIN: '/admin',
        TEACHER: '/teacher',
        STUDENT: '/student',
        CASHIER: '/cashier',
      };

      const redirectPath = dashboardRoutes[userRole as keyof typeof dashboardRoutes] || '/dashboard';
      return NextResponse.redirect(new URL(redirectPath, request.url));
    }

    // Special handling for root dashboard route - redirect to role-specific dashboard
    if (pathname === '/dashboard') {
      const dashboardRoutes = {
        ADMIN: '/admin',
        TEACHER: '/teacher',
        STUDENT: '/student',
        CASHIER: '/cashier',
      };

      const redirectPath = dashboardRoutes[userRole as keyof typeof dashboardRoutes];
      if (redirectPath) {
        return NextResponse.redirect(new URL(redirectPath, request.url));
      }
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
