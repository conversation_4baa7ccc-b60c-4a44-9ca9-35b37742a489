# Enrollment System

A comprehensive web-based enrollment management system for educational institutions built with Next.js, TypeScript, and Supabase.

## 🚀 Features

### Role-Based Access Control
- **Admin**: Complete system management, user administration, reports
- **Teacher**: Class management, grade submission, student tracking
- **Student**: Course enrollment, schedule viewing, grade tracking, document uploads
- **Cashier**: Payment processing, financial reports, account management

### Core Functionality
- ✅ Secure authentication and authorization
- ✅ Role-based dashboard interfaces
- ✅ Responsive design for mobile and desktop
- ✅ Real-time data updates
- ✅ Comprehensive audit logging
- 🚧 Course and subject management
- 🚧 Enrollment processing
- 🚧 Payment management
- 🚧 Document management
- 🚧 Grade tracking
- 🚧 Announcement system

## 🛠 Tech Stack

- **Frontend**: Next.js 15, React 18, TypeScript
- **Styling**: Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Auth, Storage)
- **Icons**: Lucide React
- **Forms**: React Hook Form with Zod validation
- **State Management**: React Context API

## 📁 Project Structure

```
enrollment-system/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── admin/             # Admin dashboard pages
│   │   ├── teacher/           # Teacher dashboard pages
│   │   ├── student/           # Student dashboard pages
│   │   ├── cashier/           # Cashier dashboard pages
│   │   └── auth/              # Authentication pages
│   ├── components/            # Reusable components
│   │   ├── ui/                # Basic UI components
│   │   ├── layout/            # Layout components
│   │   ├── forms/             # Form components
│   │   └── dashboard/         # Dashboard-specific components
│   ├── contexts/              # React contexts
│   ├── hooks/                 # Custom hooks
│   ├── lib/                   # Utility libraries
│   │   ├── auth/              # Authentication utilities
│   │   ├── database/          # Database utilities
│   │   └── utils/             # General utilities
│   └── types/                 # TypeScript type definitions
├── database/                  # Database schema and setup
│   ├── schema.sql             # Main database schema
│   ├── rls-policies.sql       # Row Level Security policies
│   ├── sample-data.sql        # Sample data for testing
│   └── README.md              # Database setup instructions
└── public/                    # Static assets
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ and npm
- A Supabase account and project

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd enrollment-system
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.local.example .env.local
   ```

   Update `.env.local` with your Supabase credentials:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

4. **Set up the database**

   Follow the instructions in `database/README.md` to set up your Supabase database:
   - Run `database/schema.sql` to create tables and functions
   - Run `database/rls-policies.sql` to set up security policies
   - Optionally run `database/sample-data.sql` for test data

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**

   Navigate to [http://localhost:3000](http://localhost:3000)

## 🔐 Authentication

The system uses Supabase Auth with custom user profiles and role-based access control.

### Demo Credentials (if using sample data)
- **Admin**: <EMAIL> / admin123
- **Teacher**: <EMAIL> / teacher123
- **Student**: <EMAIL> / student123
- **Cashier**: <EMAIL> / cashier123

## 🎨 UI Components

The system includes a comprehensive set of reusable UI components:

- **Layout Components**: Navigation, Dashboard Layout, Page Container
- **UI Components**: Button, Input, Alert, Badge, Table, Modal
- **Form Components**: Form validation and submission handling
- **Dashboard Components**: Stats cards, charts, data tables

## 🔒 Security Features

- **Row Level Security (RLS)**: Database-level access control
- **Role-based Authorization**: Route and component-level permissions
- **Secure Authentication**: Supabase Auth with JWT tokens
- **Audit Logging**: Comprehensive activity tracking
- **Input Validation**: Client and server-side validation

## 📱 Responsive Design

The application is fully responsive and works seamlessly across:
- Desktop computers
- Tablets
- Mobile phones

## 🚧 Development Status

### Completed ✅
- Project setup and architecture
- Database schema and security policies
- Authentication system with role-based access
- Core UI components and layout system
- Basic dashboard interfaces for all roles
- Responsive navigation system

### In Progress 🚧
- Admin dashboard features
- Teacher dashboard features
- Student dashboard features
- Cashier dashboard features

### Planned 📋
- Testing and quality assurance
- Documentation and deployment guides

## 🧪 Testing

```bash
# Run tests (when implemented)
npm test

# Run linting
npm run lint

# Type checking
npm run type-check
```

## 📦 Building for Production

```bash
# Build the application
npm run build

# Start production server
npm start
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Check the documentation in the `database/` folder
- Review the component examples in `src/components/`
- Check the type definitions in `src/types/`

## 🔮 Future Enhancements

- Real-time notifications
- Advanced reporting and analytics
- Mobile app development
- Integration with external systems
- Advanced scheduling features
- Automated email notifications
- Document verification workflows
- Payment gateway integration
