# 🚀 SQLite Setup Guide - Enrollment System

This guide will help you run the Enrollment System using SQLite instead of Supabase. This setup is much simpler and doesn't require any external services.

## ✅ What You Need

- **Node.js 18+** installed on your computer
- **npm** (comes with Node.js)
- That's it! No database setup required.

## 🏃‍♂️ Quick Start (5 minutes)

### 1. Install Dependencies
```bash
cd enrollment-system
npm install
```

### 2. Set Up Database
```bash
# Generate Prisma client
npx prisma generate

# Create SQLite database and tables
npx prisma db push

# Add sample data (demo users, courses, etc.)
npm run db:seed
```

### 3. Start the Application
```bash
npm run dev
```

### 4. Open Your Browser
Go to [http://localhost:3000](http://localhost:3000)

## 🔐 Demo Login Credentials

The seed script creates these demo accounts:

| Role | Email | Password |
|------|-------|----------|
| **Admin** | <EMAIL> | admin123 |
| **Teacher** | <EMAIL> | teacher123 |
| **Student** | <EMAIL> | student123 |
| **Cashier** | <EMAIL> | cashier123 |

## 🎯 What You'll See

### For Students:
- Dashboard with enrollment overview
- Quick actions for enrollment, schedule, grades, payments
- Current class schedule and announcements

### For Admins:
- System statistics and user management
- Quick actions for managing users, courses, and reports
- System alerts and recent activity

### For Teachers:
- Class management and student rosters
- Grade submission interface
- Announcement creation tools

### For Cashiers:
- Payment processing interface
- Student account management
- Financial reporting tools

## 🗄️ Database Information

- **Database Type**: SQLite
- **Location**: `prisma/dev.db` (created automatically)
- **ORM**: Prisma
- **Schema**: Defined in `prisma/schema.prisma`

## 🔧 Useful Commands

```bash
# Reset database and reseed
npm run db:reset

# View database in Prisma Studio
npx prisma studio

# Generate new Prisma client after schema changes
npx prisma generate

# Apply schema changes to database
npx prisma db push

# Seed database with sample data
npm run db:seed
```

## 📊 Database Schema

The system includes these main entities:

- **Users** - Admin, Teacher, Student, Cashier accounts
- **Courses** - Degree programs (e.g., Computer Science)
- **Subjects** - Individual subjects (e.g., CS101)
- **Classes** - Specific class instances with schedules
- **Enrollments** - Student enrollments in classes
- **Payments** - Student payment records
- **Grades** - Individual grade entries
- **Announcements** - System-wide announcements
- **Documents** - Student document uploads
- **Audit Logs** - System activity tracking

## 🔍 Exploring the Data

You can view and edit the database using Prisma Studio:

```bash
npx prisma studio
```

This opens a web interface at `http://localhost:5555` where you can:
- View all tables and data
- Add, edit, or delete records
- Explore relationships between tables

## 🚧 Development Tips

### Adding New Users
1. Use the registration page: `http://localhost:3000/auth/register`
2. Or add directly via Prisma Studio
3. Or modify the seed script in `prisma/seed.ts`

### Modifying the Database Schema
1. Edit `prisma/schema.prisma`
2. Run `npx prisma db push` to apply changes
3. Run `npx prisma generate` to update the client

### Resetting Everything
```bash
npm run db:reset
```
This will:
- Delete the database
- Recreate all tables
- Add fresh sample data

## 🆘 Troubleshooting

### "Database file not found"
```bash
npx prisma db push
```

### "Prisma client not generated"
```bash
npx prisma generate
```

### "Port 3000 already in use"
```bash
# Kill the process using port 3000
npx kill-port 3000
# Or use a different port
npm run dev -- -p 3001
```

### "Authentication not working"
- Make sure you're using the correct demo credentials
- Check that the seed script ran successfully
- Try resetting the database: `npm run db:reset`

## 🎉 Success!

If everything is working correctly, you should have:
- ✅ A running enrollment system at `http://localhost:3000`
- ✅ Working authentication with demo accounts
- ✅ Role-based dashboards for different user types
- ✅ Sample data to explore the system

The SQLite version is perfect for:
- **Development and testing**
- **Local demonstrations**
- **Learning the system**
- **Prototyping new features**

For production use, you might want to consider PostgreSQL or MySQL, but SQLite works great for getting started!

## 🔄 Converting Back to PostgreSQL

If you later want to use PostgreSQL instead of SQLite:

1. Update `prisma/schema.prisma` datasource to PostgreSQL
2. Update `DATABASE_URL` in `.env.local`
3. Run `npx prisma db push`
4. Run `npm run db:seed`

The application code doesn't need to change - Prisma handles the database differences automatically!
