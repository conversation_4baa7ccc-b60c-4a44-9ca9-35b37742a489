// Enrollment System Database Schema
// SQLite with Prisma ORM

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// Enums
enum UserRole {
  ADMIN
  TEACHER
  STUDENT
  CASHIER
}

enum EnrollmentStatus {
  PENDING
  ENROLLED
  DROPPED
  COMPLETED
}

enum PaymentType {
  TUITION
  MISCELLANEOUS
  LABORATORY
  LIBRARY
  REGISTRATION
  OTHER
}

enum PaymentStatus {
  PENDING
  PAID
  OVERDUE
  CANCELLED
}

enum DocumentType {
  TRANSCRIPT
  DIPLOMA
  BIRTH_CERTIFICATE
  ID_PHOTO
  MEDICAL_CERTIFICATE
  OTHER
}

enum DocumentStatus {
  PENDING
  VERIFIED
  REJECTED
}

enum GradeType {
  QUIZ
  EXAM
  ASSIGNMENT
  PROJECT
  PARTICIPATION
  FINAL
}

// User and Authentication Models
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  firstName String
  lastName  String
  role      UserRole @default(STUDENT)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  profile              UserProfile?
  teachingClasses      Class[]              @relation("TeacherClasses")
  enrollments          Enrollment[]
  payments             Payment[]
  documents            Document[]
  authoredAnnouncements Announcement[]      @relation("AnnouncementAuthor")
  submittedGrades      Grade[]              @relation("GradeSubmitter")
  processedPayments    Payment[]            @relation("PaymentProcessor")
  verifiedDocuments    Document[]           @relation("DocumentVerifier")
  auditLogs            AuditLog[]

  @@map("users")
}

model UserProfile {
  id               String    @id @default(cuid())
  userId           String    @unique
  phone            String?
  address          String?
  dateOfBirth      DateTime?
  profileImage     String?
  emergencyContact String?
  emergencyPhone   String?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_profiles")
}

// Academic Models
model Course {
  id          String   @id @default(cuid())
  name        String
  description String?
  duration    Int      @default(8) // in semesters
  totalUnits  Int      @default(0)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  classes Class[]

  @@map("courses")
}

model Subject {
  id            String   @id @default(cuid())
  code          String   @unique
  name          String
  description   String?
  units         Int      @default(3)
  prerequisites String? // JSON string array of subject codes
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  classes Class[]

  @@map("subjects")
}

model AcademicPeriod {
  id              String   @id @default(cuid())
  name            String
  academicYear    String
  semester        Int // 1=First, 2=Second, 3=Summer
  startDate       DateTime
  endDate         DateTime
  enrollmentStart DateTime
  enrollmentEnd   DateTime
  isActive        Boolean  @default(false)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  classes Class[]

  @@unique([academicYear, semester])
  @@map("academic_periods")
}

model Class {
  id               String   @id @default(cuid())
  subjectId        String
  teacherId        String?
  courseId         String?
  academicPeriodId String
  section          String
  maxStudents      Int      @default(30)
  enrolledCount    Int      @default(0)
  isActive         Boolean  @default(true)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relations
  subject        Subject        @relation(fields: [subjectId], references: [id], onDelete: Cascade)
  teacher        User?          @relation("TeacherClasses", fields: [teacherId], references: [id], onDelete: SetNull)
  course         Course?        @relation(fields: [courseId], references: [id], onDelete: SetNull)
  academicPeriod AcademicPeriod @relation(fields: [academicPeriodId], references: [id], onDelete: Cascade)
  schedules      ClassSchedule[]
  enrollments    Enrollment[]

  @@unique([subjectId, section, academicPeriodId])
  @@map("classes")
}

model ClassSchedule {
  id        String   @id @default(cuid())
  classId   String
  dayOfWeek Int // 0=Sunday, 6=Saturday
  startTime String // HH:MM format
  endTime   String // HH:MM format
  room      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  class Class @relation(fields: [classId], references: [id], onDelete: Cascade)

  @@map("class_schedules")
}

// Enrollment Models
model Enrollment {
  id         String           @id @default(cuid())
  studentId  String
  classId    String
  status     EnrollmentStatus @default(PENDING)
  enrolledAt DateTime         @default(now())
  grade      Float?
  remarks    String?
  createdAt  DateTime         @default(now())
  updatedAt  DateTime         @updatedAt

  // Relations
  student User   @relation(fields: [studentId], references: [id], onDelete: Cascade)
  class   Class  @relation(fields: [classId], references: [id], onDelete: Cascade)
  grades  Grade[]

  @@unique([studentId, classId])
  @@map("enrollments")
}

model Grade {
  id           String    @id @default(cuid())
  enrollmentId String
  gradeType    GradeType
  score        Float
  maxScore     Float
  weight       Float     @default(1.0)
  description  String?
  gradedAt     DateTime  @default(now())
  gradedBy     String?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  // Relations
  enrollment   Enrollment @relation(fields: [enrollmentId], references: [id], onDelete: Cascade)
  gradedByUser User?      @relation("GradeSubmitter", fields: [gradedBy], references: [id], onDelete: SetNull)

  @@map("grades")
}

// Payment Models
model Payment {
  id            String        @id @default(cuid())
  studentId     String
  amount        Float
  paymentType   PaymentType
  description   String
  status        PaymentStatus @default(PENDING)
  paidAt        DateTime?
  dueDate       DateTime?
  receiptNumber String?
  processedBy   String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  student         User  @relation(fields: [studentId], references: [id], onDelete: Cascade)
  processedByUser User? @relation("PaymentProcessor", fields: [processedBy], references: [id], onDelete: SetNull)

  @@map("payments")
}

// Document Models
model Document {
  id           String         @id @default(cuid())
  studentId    String
  fileName     String
  originalName String
  fileType     String
  fileSize     Int
  documentType DocumentType
  status       DocumentStatus @default(PENDING)
  uploadedAt   DateTime       @default(now())
  verifiedAt   DateTime?
  verifiedBy   String?
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt

  // Relations
  student        User  @relation(fields: [studentId], references: [id], onDelete: Cascade)
  verifiedByUser User? @relation("DocumentVerifier", fields: [verifiedBy], references: [id], onDelete: SetNull)

  @@map("documents")
}

// Communication Models
model Announcement {
  id            String     @id @default(cuid())
  title         String
  content       String
  authorId      String
  targetRoles   String // JSON string array of roles
  targetClasses String? // JSON string array of class IDs
  isActive      Boolean    @default(true)
  publishedAt   DateTime?
  expiresAt     DateTime?
  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt

  // Relations
  author User @relation("AnnouncementAuthor", fields: [authorId], references: [id], onDelete: Cascade)

  @@map("announcements")
}

// Audit Models
model AuditLog {
  id         String   @id @default(cuid())
  userId     String?
  action     String
  entityType String
  entityId   String
  oldValues  String? // JSON string
  newValues  String? // JSON string
  ipAddress  String?
  userAgent  String?
  createdAt  DateTime @default(now())

  // Relations
  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("audit_logs")
}
