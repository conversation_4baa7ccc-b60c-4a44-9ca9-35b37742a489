import { PrismaClient } from '@prisma/client';
import { hashPassword } from '../src/lib/auth';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seed...');

  // Create admin user
  const adminPassword = await hashPassword('admin123');
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: adminPassword,
      firstName: 'System',
      lastName: 'Administrator',
      role: 'ADMIN',
    },
  });

  // Create teacher user
  const teacherPassword = await hashPassword('teacher123');
  const teacher = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: teacherPassword,
      firstName: 'John',
      lastName: 'Doe',
      role: 'TEACHER',
    },
  });

  // Create student user
  const studentPassword = await hashPassword('student123');
  const student = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: studentPassword,
      firstName: 'Alice',
      lastName: 'Johnson',
      role: 'STUDENT',
    },
  });

  // Create cashier user
  const cashierPassword = await hashPassword('cashier123');
  const cashier = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: cashierPassword,
      firstName: 'Mary',
      lastName: 'Smith',
      role: 'CASHIER',
    },
  });

  // Create courses
  const csCourse = await prisma.course.upsert({
    where: { id: 'cs-course-1' },
    update: {},
    create: {
      id: 'cs-course-1',
      name: 'Bachelor of Science in Computer Science',
      description: 'A comprehensive program covering software development, algorithms, and computer systems.',
      duration: 8,
      totalUnits: 120,
    },
  });

  const itCourse = await prisma.course.upsert({
    where: { id: 'it-course-1' },
    update: {},
    create: {
      id: 'it-course-1',
      name: 'Bachelor of Science in Information Technology',
      description: 'Focuses on the application of technology in business and organizations.',
      duration: 8,
      totalUnits: 120,
    },
  });

  // Create subjects
  const cs101 = await prisma.subject.upsert({
    where: { code: 'CS101' },
    update: {},
    create: {
      code: 'CS101',
      name: 'Introduction to Programming',
      description: 'Basic programming concepts using Python',
      units: 3,
      prerequisites: '[]',
    },
  });

  const math101 = await prisma.subject.upsert({
    where: { code: 'MATH101' },
    update: {},
    create: {
      code: 'MATH101',
      name: 'College Algebra',
      description: 'Fundamental algebraic concepts',
      units: 3,
      prerequisites: '[]',
    },
  });

  const eng101 = await prisma.subject.upsert({
    where: { code: 'ENG101' },
    update: {},
    create: {
      code: 'ENG101',
      name: 'English Composition',
      description: 'Academic writing and communication',
      units: 3,
      prerequisites: '[]',
    },
  });

  // Create academic period
  const academicPeriod = await prisma.academicPeriod.upsert({
    where: { 
      academicYear_semester: {
        academicYear: '2024-2025',
        semester: 1,
      }
    },
    update: {},
    create: {
      name: 'First Semester AY 2024-2025',
      academicYear: '2024-2025',
      semester: 1,
      startDate: new Date('2024-08-15'),
      endDate: new Date('2024-12-15'),
      enrollmentStart: new Date('2024-07-01'),
      enrollmentEnd: new Date('2024-08-10'),
      isActive: true,
    },
  });

  // Create classes
  const cs101ClassA = await prisma.class.upsert({
    where: { 
      subjectId_section_academicPeriodId: {
        subjectId: cs101.id,
        section: 'A',
        academicPeriodId: academicPeriod.id,
      }
    },
    update: {},
    create: {
      subjectId: cs101.id,
      teacherId: teacher.id,
      courseId: csCourse.id,
      academicPeriodId: academicPeriod.id,
      section: 'A',
      maxStudents: 30,
    },
  });

  const math101ClassA = await prisma.class.upsert({
    where: { 
      subjectId_section_academicPeriodId: {
        subjectId: math101.id,
        section: 'A',
        academicPeriodId: academicPeriod.id,
      }
    },
    update: {},
    create: {
      subjectId: math101.id,
      teacherId: teacher.id,
      courseId: csCourse.id,
      academicPeriodId: academicPeriod.id,
      section: 'A',
      maxStudents: 35,
    },
  });

  // Create class schedules
  await prisma.classSchedule.createMany({
    data: [
      {
        classId: cs101ClassA.id,
        dayOfWeek: 1, // Monday
        startTime: '08:00',
        endTime: '09:30',
        room: 'Room 101',
      },
      {
        classId: cs101ClassA.id,
        dayOfWeek: 3, // Wednesday
        startTime: '08:00',
        endTime: '09:30',
        room: 'Room 101',
      },
      {
        classId: cs101ClassA.id,
        dayOfWeek: 5, // Friday
        startTime: '08:00',
        endTime: '09:30',
        room: 'Lab 1',
      },
      {
        classId: math101ClassA.id,
        dayOfWeek: 2, // Tuesday
        startTime: '14:00',
        endTime: '15:30',
        room: 'Room 201',
      },
      {
        classId: math101ClassA.id,
        dayOfWeek: 4, // Thursday
        startTime: '14:00',
        endTime: '15:30',
        room: 'Room 201',
      },
    ],
  });

  // Create sample enrollments
  const enrollment1 = await prisma.enrollment.upsert({
    where: {
      studentId_classId: {
        studentId: student.id,
        classId: cs101ClassA.id,
      }
    },
    update: {},
    create: {
      studentId: student.id,
      classId: cs101ClassA.id,
      status: 'ENROLLED',
    },
  });

  const enrollment2 = await prisma.enrollment.upsert({
    where: {
      studentId_classId: {
        studentId: student.id,
        classId: math101ClassA.id,
      }
    },
    update: {},
    create: {
      studentId: student.id,
      classId: math101ClassA.id,
      status: 'ENROLLED',
    },
  });

  // Update enrolled count
  await prisma.class.update({
    where: { id: cs101ClassA.id },
    data: { enrolledCount: 1 },
  });

  await prisma.class.update({
    where: { id: math101ClassA.id },
    data: { enrolledCount: 1 },
  });

  // Create sample payments
  await prisma.payment.createMany({
    data: [
      {
        studentId: student.id,
        amount: 25000.00,
        paymentType: 'TUITION',
        description: 'Tuition Fee - First Semester AY 2024-2025',
        status: 'PENDING',
        dueDate: new Date('2024-08-15'),
      },
      {
        studentId: student.id,
        amount: 2000.00,
        paymentType: 'MISCELLANEOUS',
        description: 'Miscellaneous Fees',
        status: 'PENDING',
        dueDate: new Date('2024-08-15'),
      },
    ],
  });

  // Create sample announcements
  await prisma.announcement.createMany({
    data: [
      {
        title: 'Welcome to the New Academic Year!',
        content: 'We are excited to welcome all students to the Academic Year 2024-2025. Please check your enrollment status and payment requirements.',
        authorId: admin.id,
        targetRoles: '["STUDENT", "TEACHER"]',
        isActive: true,
        publishedAt: new Date(),
      },
      {
        title: 'Faculty Meeting Schedule',
        content: 'All faculty members are required to attend the monthly meeting on Friday, 2:00 PM in the Conference Room.',
        authorId: admin.id,
        targetRoles: '["TEACHER"]',
        isActive: true,
        publishedAt: new Date(),
      },
      {
        title: 'Payment Deadline Reminder',
        content: 'This is a reminder that tuition payments are due by August 15, 2024. Please settle your accounts to avoid late fees.',
        authorId: admin.id,
        targetRoles: '["STUDENT"]',
        isActive: true,
        publishedAt: new Date(),
      },
    ],
  });

  console.log('✅ Database seeded successfully!');
  console.log('📧 Demo accounts created:');
  console.log('   Admin: <EMAIL> / admin123');
  console.log('   Teacher: <EMAIL> / teacher123');
  console.log('   Student: <EMAIL> / student123');
  console.log('   Cashier: <EMAIL> / cashier123');
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
